import { ExecutionContext } from "@nestjs/common";
import { GqlExecutionContext } from "@nestjs/graphql";

/**
 * Interface for request objects that may contain user information
 */
export interface IRequestWithUser<T = any> {
  user?: T;
  [key: string]: any;
}

/**
 * Type guard to check if an object has a req property
 */
function isObjectWithReq(value: unknown): value is { req: unknown } {
  return (
    value !== null &&
    typeof value === "object" &&
    "req" in value &&
    value.req !== null &&
    value.req !== undefined
  );
}

/**
 * Utility function to extract request object from both REST and GraphQL execution contexts
 * @param context - NestJS ExecutionContext
 * @returns The request object from either GraphQL or HTTP context
 */
export function getRequestFromContext<T = any>(
  context: ExecutionContext,
): IRequestWithUser<T> {
  // Handle both REST and GraphQL contexts
  const ctx = GqlExecutionContext.create(context);
  
  // Try to get request from GraphQL context first
  let request: IRequestWithUser<T> | null = null;
  try {
    const gqlContext: unknown = ctx.getContext();
    if (isObjectWithReq(gqlContext)) {
      request = gqlContext.req as IRequestWithUser<T>;
    }
  } catch {
    request = null;
  }
  
  // Fallback to HTTP context if GraphQL context doesn't have request
  if (!request) {
    request = context.switchToHttp().getRequest<IRequestWithUser<T>>();
  }
  
  return request;
}
