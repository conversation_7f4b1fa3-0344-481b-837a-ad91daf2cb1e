import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { GqlExecutionContext } from "@nestjs/graphql";
import { Request } from "express";
import { CognitoAuthService } from "./cognito-auth.service";
import { IS_PUBLIC_KEY } from "./public.decorator";
import { getRequestWithContext } from "./utils/context.utils";

@Injectable()
export class CognitoAuthGuard implements CanActivate {
  constructor(
    private readonly cognitoAuthService: CognitoAuthService,
    private readonly reflector: Reflector,
  ) {}

  private isGraphQLContext(
    contextValue: unknown,
  ): contextValue is { req: Request } {
    if (
      contextValue !== null &&
      contextValue !== undefined &&
      typeof contextValue === "object"
    ) {
      return (
        "req" in contextValue &&
        contextValue.req !== null &&
        contextValue.req !== undefined &&
        typeof contextValue.req === "object"
      );
    }
    return false;
  }

  private getRequest(context: ExecutionContext): Request {
    // Try GraphQL context first
    const gqlContext = GqlExecutionContext.create(context);
    const contextValue: unknown = gqlContext.getContext();

    if (this.isGraphQLContext(contextValue)) {
      return contextValue.req;
    }

    // Fall back to HTTP context
    return context.switchToHttp().getRequest<Request>();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // Handle both REST and GraphQL contexts
    const request = getRequestWithContext(context);

    const user = await this.cognitoAuthService.getUserFromRequest(request);
    if (!user) {
      throw new UnauthorizedException("Authentication required");
    }

    // Attach user to request for @CurrentUser() decorator
    request.user = user;
    return true;
  }
}
